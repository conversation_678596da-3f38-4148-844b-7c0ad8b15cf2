#!/bin/bash

# AtomSec Database Function App Deployment Script
# This script automates the deployment of the AtomSec Database Function App to Azure

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if required tools are installed
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    # Check Azure CLI
    if ! command -v az &> /dev/null; then
        print_error "Azure CLI is not installed. Please install it first."
        exit 1
    fi
    
    # Check Functions Core Tools
    if ! command -v func &> /dev/null; then
        print_error "Azure Functions Core Tools is not installed. Please install it first."
        exit 1
    fi
    
    # Check Python
    if ! command -v python &> /dev/null && ! command -v python3 &> /dev/null; then
        print_error "Python is not installed. Please install Python 3.12."
        exit 1
    fi
    
    print_success "All prerequisites are installed."
}

# Login to Azure
azure_login() {
    print_status "Checking Azure login status..."
    
    if ! az account show &> /dev/null; then
        print_status "Please log in to Azure..."
        az login
    fi
    
    # Show current subscription
    SUBSCRIPTION=$(az account show --query name -o tsv)
    print_success "Logged in to Azure. Current subscription: $SUBSCRIPTION"
}

# Set deployment variables
set_variables() {
    print_status "Setting deployment variables..."
    
    # Default values
    RESOURCE_GROUP="${RESOURCE_GROUP:-atomsec-dev-data}"
    LOCATION="${LOCATION:-East US}"
    FUNCTION_APP_NAME="${FUNCTION_APP_NAME:-func-atomsec-dbconnect-dev}"
    STORAGE_ACCOUNT="${STORAGE_ACCOUNT:-statomsecdev$(date +%s)}"
    KEY_VAULT_NAME="${KEY_VAULT_NAME:-akv-atomsec-dev}"
    
    echo "Deployment Configuration:"
    echo "  Resource Group: $RESOURCE_GROUP"
    echo "  Location: $LOCATION"
    echo "  Function App: $FUNCTION_APP_NAME"
    echo "  Storage Account: $STORAGE_ACCOUNT"
    echo "  Key Vault: $KEY_VAULT_NAME"
    echo
    
    read -p "Continue with these settings? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_error "Deployment cancelled."
        exit 1
    fi
}

# Create Azure resources
create_resources() {
    print_status "Creating Azure resources..."
    
    # Create resource group
    print_status "Creating resource group: $RESOURCE_GROUP"
    az group create --name "$RESOURCE_GROUP" --location "$LOCATION" --output none
    
    # Create storage account
    print_status "Creating storage account: $STORAGE_ACCOUNT"
    az storage account create \
        --name "$STORAGE_ACCOUNT" \
        --resource-group "$RESOURCE_GROUP" \
        --location "$LOCATION" \
        --sku Standard_LRS \
        --kind StorageV2 \
        --output none
    
    # Create Key Vault
    print_status "Creating Key Vault: $KEY_VAULT_NAME"
    az keyvault create \
        --name "$KEY_VAULT_NAME" \
        --resource-group "$RESOURCE_GROUP" \
        --location "$LOCATION" \
        --sku standard \
        --output none
    
    # Create Function App
    print_status "Creating Function App: $FUNCTION_APP_NAME"
    az functionapp create \
        --name "$FUNCTION_APP_NAME" \
        --resource-group "$RESOURCE_GROUP" \
        --storage-account "$STORAGE_ACCOUNT" \
        --runtime python \
        --runtime-version 3.12 \
        --functions-version 4 \
        --os-type Linux \
        --consumption-plan-location "$LOCATION" \
        --output none
    
    print_success "Azure resources created successfully."
}

# Configure Function App
configure_function_app() {
    print_status "Configuring Function App..."
    
    # Get storage connection string
    STORAGE_CONNECTION=$(az storage account show-connection-string \
        --name "$STORAGE_ACCOUNT" \
        --resource-group "$RESOURCE_GROUP" \
        --query connectionString -o tsv)
    
    # Set application settings
    az functionapp config appsettings set \
        --name "$FUNCTION_APP_NAME" \
        --resource-group "$RESOURCE_GROUP" \
        --settings \
            "AZURE_STORAGE_CONNECTION_STRING=$STORAGE_CONNECTION" \
            "KEY_VAULT_NAME=$KEY_VAULT_NAME" \
            "ENVIRONMENT=production" \
            "IS_LOCAL_DEV=false" \
            "FUNCTIONS_WORKER_RUNTIME=python" \
        --output none
    
    # Enable managed identity
    print_status "Enabling managed identity..."
    az functionapp identity assign \
        --name "$FUNCTION_APP_NAME" \
        --resource-group "$RESOURCE_GROUP" \
        --output none
    
    # Get principal ID and set Key Vault access
    PRINCIPAL_ID=$(az functionapp identity show \
        --name "$FUNCTION_APP_NAME" \
        --resource-group "$RESOURCE_GROUP" \
        --query principalId -o tsv)
    
    az keyvault set-policy \
        --name "$KEY_VAULT_NAME" \
        --object-id "$PRINCIPAL_ID" \
        --secret-permissions get list \
        --output none
    
    print_success "Function App configured successfully."
}

# Deploy the application
deploy_application() {
    print_status "Deploying application..."
    
    # Deploy using Functions Core Tools
    func azure functionapp publish "$FUNCTION_APP_NAME" --python
    
    print_success "Application deployed successfully."
}

# Test deployment
test_deployment() {
    print_status "Testing deployment..."
    
    # Wait a moment for the deployment to be ready
    sleep 30
    
    # Test health endpoint
    HEALTH_URL="https://$FUNCTION_APP_NAME.azurewebsites.net/api/db/health"
    print_status "Testing health endpoint: $HEALTH_URL"
    
    if curl -f -s "$HEALTH_URL" > /dev/null; then
        print_success "Health endpoint is responding."
    else
        print_warning "Health endpoint is not responding yet. This might be normal for a new deployment."
    fi
    
    # Test info endpoint
    INFO_URL="https://$FUNCTION_APP_NAME.azurewebsites.net/api/db/info"
    print_status "Testing info endpoint: $INFO_URL"
    
    if curl -f -s "$INFO_URL" > /dev/null; then
        print_success "Info endpoint is responding."
    else
        print_warning "Info endpoint is not responding yet."
    fi
}

# Main deployment function
main() {
    echo "=========================================="
    echo "AtomSec Database Function App Deployment"
    echo "=========================================="
    echo
    
    check_prerequisites
    azure_login
    set_variables
    create_resources
    configure_function_app
    deploy_application
    test_deployment
    
    echo
    print_success "Deployment completed successfully!"
    echo
    echo "Function App URL: https://$FUNCTION_APP_NAME.azurewebsites.net"
    echo "Health Check: https://$FUNCTION_APP_NAME.azurewebsites.net/api/db/health"
    echo "Info Endpoint: https://$FUNCTION_APP_NAME.azurewebsites.net/api/db/info"
    echo
    print_warning "Don't forget to:"
    echo "1. Add required secrets to Key Vault: $KEY_VAULT_NAME"
    echo "2. Configure SQL Database connection string"
    echo "3. Set up monitoring and alerts"
    echo "4. Update CORS settings if needed"
}

# Run main function
main "$@"
