#!/bin/bash

# AtomSec Database Function App - Deploy to Existing Infrastructure
# This script deploys to your existing Azure resources

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Existing resource configuration
RESOURCE_GROUP="atomsec-dev-data"
FUNCTION_APP_NAME="func-atomsec-dbconnect-dev"
KEY_VAULT_NAME="akv-atomsec-dev"
SQL_SERVER="sqldb-atomsec-dev"
SQL_DATABASE="sql-atomsec-dev"
SERVICE_BUS_NAMESPACE="atomsec"

echo "=========================================="
echo "AtomSec DB Function App - Deploy to Existing"
echo "=========================================="
echo
echo "Target Resources:"
echo "  Function App: $FUNCTION_APP_NAME"
echo "  Resource Group: $RESOURCE_GROUP"
echo "  Key Vault: $KEY_VAULT_NAME"
echo "  SQL Server: $SQL_SERVER"
echo "  SQL Database: $SQL_DATABASE"
echo

# Check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    if ! command -v az &> /dev/null; then
        print_error "Azure CLI is not installed."
        exit 1
    fi
    
    if ! command -v func &> /dev/null; then
        print_error "Azure Functions Core Tools is not installed."
        exit 1
    fi
    
    print_success "Prerequisites check passed."
}

# Login to Azure
azure_login() {
    print_status "Checking Azure login status..."
    
    if ! az account show &> /dev/null; then
        print_status "Please log in to Azure..."
        az login
    fi
    
    SUBSCRIPTION=$(az account show --query name -o tsv)
    print_success "Logged in to Azure. Subscription: $SUBSCRIPTION"
}

# Verify existing resources
verify_resources() {
    print_status "Verifying existing resources..."
    
    # Check Function App
    if az functionapp show --name "$FUNCTION_APP_NAME" --resource-group "$RESOURCE_GROUP" &> /dev/null; then
        print_success "Function App '$FUNCTION_APP_NAME' found."
    else
        print_error "Function App '$FUNCTION_APP_NAME' not found in resource group '$RESOURCE_GROUP'."
        exit 1
    fi
    
    # Check Key Vault
    if az keyvault show --name "$KEY_VAULT_NAME" &> /dev/null; then
        print_success "Key Vault '$KEY_VAULT_NAME' found."
    else
        print_error "Key Vault '$KEY_VAULT_NAME' not found."
        exit 1
    fi
    
    # Check SQL Database
    if az sql db show --name "$SQL_DATABASE" --server "$SQL_SERVER" --resource-group "$RESOURCE_GROUP" &> /dev/null; then
        print_success "SQL Database '$SQL_DATABASE' found."
    else
        print_warning "SQL Database '$SQL_DATABASE' not found. This might be okay if using Table Storage only."
    fi
}

# Configure Function App settings
configure_function_app() {
    print_status "Configuring Function App settings..."
    
    # Get storage account for the Function App
    STORAGE_ACCOUNT=$(az functionapp config appsettings list \
        --name "$FUNCTION_APP_NAME" \
        --resource-group "$RESOURCE_GROUP" \
        --query "[?name=='AzureWebJobsStorage'].value" -o tsv)
    
    if [[ -z "$STORAGE_ACCOUNT" ]]; then
        print_warning "Could not determine storage account from existing settings."
    fi
    
    # Configure application settings
    print_status "Setting application configuration..."
    
    az functionapp config appsettings set \
        --name "$FUNCTION_APP_NAME" \
        --resource-group "$RESOURCE_GROUP" \
        --settings \
            "KEY_VAULT_NAME=$KEY_VAULT_NAME" \
            "KEY_VAULT_URL=https://$KEY_VAULT_NAME.vault.azure.net/" \
            "ENVIRONMENT=production" \
            "IS_LOCAL_DEV=false" \
            "FUNCTIONS_WORKER_RUNTIME=python" \
            "SFDC_SERVICE_URL=https://apim-atomsec-dev.azure-api.net" \
        --output none
    
    print_success "Function App settings configured."
}

# Enable managed identity and Key Vault access
setup_managed_identity() {
    print_status "Setting up managed identity and Key Vault access..."
    
    # Enable system-assigned managed identity
    PRINCIPAL_ID=$(az functionapp identity assign \
        --name "$FUNCTION_APP_NAME" \
        --resource-group "$RESOURCE_GROUP" \
        --query principalId -o tsv)
    
    if [[ -n "$PRINCIPAL_ID" ]]; then
        print_success "Managed identity enabled. Principal ID: $PRINCIPAL_ID"
        
        # Grant Key Vault access
        az keyvault set-policy \
            --name "$KEY_VAULT_NAME" \
            --object-id "$PRINCIPAL_ID" \
            --secret-permissions get list \
            --output none
        
        print_success "Key Vault access granted to Function App."
    else
        print_error "Failed to enable managed identity."
        exit 1
    fi
}

# Deploy the application
deploy_application() {
    print_status "Deploying application to Function App..."
    
    # Deploy using Functions Core Tools
    func azure functionapp publish "$FUNCTION_APP_NAME" --python
    
    print_success "Application deployed successfully."
}

# Test deployment
test_deployment() {
    print_status "Testing deployment..."
    
    # Wait for deployment to be ready
    sleep 30
    
    # Test health endpoint
    HEALTH_URL="https://$FUNCTION_APP_NAME.azurewebsites.net/api/db/health"
    print_status "Testing health endpoint: $HEALTH_URL"
    
    if curl -f -s "$HEALTH_URL" > /dev/null; then
        print_success "Health endpoint is responding."
        
        # Show health status
        print_status "Health check response:"
        curl -s "$HEALTH_URL" | python -m json.tool || echo "Could not parse JSON response"
    else
        print_warning "Health endpoint is not responding yet. This might be normal for a new deployment."
        print_status "You can check the status later at: $HEALTH_URL"
    fi
    
    # Test info endpoint
    INFO_URL="https://$FUNCTION_APP_NAME.azurewebsites.net/api/db/info"
    print_status "Info endpoint: $INFO_URL"
}

# Show post-deployment information
show_deployment_info() {
    echo
    print_success "Deployment completed!"
    echo
    echo "Function App Details:"
    echo "  Name: $FUNCTION_APP_NAME"
    echo "  URL: https://$FUNCTION_APP_NAME.azurewebsites.net"
    echo "  Health Check: https://$FUNCTION_APP_NAME.azurewebsites.net/api/db/health"
    echo "  Info Endpoint: https://$FUNCTION_APP_NAME.azurewebsites.net/api/db/info"
    echo
    echo "API Endpoints (all prefixed with /api/db/):"
    echo "  - GET /api/db/users"
    echo "  - GET /api/db/accounts"
    echo "  - GET /api/db/organizations"
    echo "  - GET /api/db/integrations"
    echo "  - GET /api/db/security/health-checks"
    echo "  - GET /api/db/tasks"
    echo "  - POST /api/db/auth/login"
    echo
    print_warning "Next steps:"
    echo "1. Verify Key Vault secrets are configured:"
    echo "   - jwt-secret"
    echo "   - sql-connection-string"
    echo "   - azure-ad-* secrets (if using Azure AD)"
    echo
    echo "2. Test the API endpoints"
    echo "3. Check Application Insights for monitoring"
    echo "4. Update any client applications to use the new endpoints"
}

# Main execution
main() {
    check_prerequisites
    azure_login
    verify_resources
    configure_function_app
    setup_managed_identity
    deploy_application
    test_deployment
    show_deployment_info
}

# Run main function
main "$@"
